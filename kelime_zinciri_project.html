<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON></title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .project-title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        .section h2 {
            color: #333;
            margin-top: 0;
            font-size: 1.5rem;
        }
        .file-structure {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            line-height: 1.5;
        }
        .folder { color: #ffd700; }
        .file { color: #98fb98; }
        .config { color: #87ceeb; }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .tech-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #667eea;
        }
        .tech-item h3 {
            margin-top: 0;
            color: #333;
        }
        .download-section {
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }
        .download-btn {
            background: white;
            color: #667eea;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .feature {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .feature h4 {
            color: #667eea;
            margin-top: 0;
        }
        .api-endpoint {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
        }
        .method-get { border-left: 4px solid #48bb78; }
        .method-post { border-left: 4px solid #4299e1; }
        .method-put { border-left: 4px solid #ed8936; }
        .method-delete { border-left: 4px solid #f56565; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="project-title">🎯 Kelime Zinciri</h1>
            <p class="subtitle">Tam Stack Oyun Projesi - .NET Core Backend + React Frontend</p>
        </div>

        <div class="section">
            <h2>📁 Proje Yapısı</h2>
            <div class="file-structure">
KelimeZinciri/
├── <span class="folder">📁 Backend/</span>
│   ├── <span class="folder">📁 KelimeZinciri.API/</span>
│   │   ├── <span class="folder">📁 Controllers/</span>
│   │   │   ├── <span class="file">🎮 GameController.cs</span>
│   │   │   ├── <span class="file">👤 UserController.cs</span>
│   │   │   ├── <span class="file">📊 StatisticsController.cs</span>
│   │   │   └── <span class="file">🏆 LeaderboardController.cs</span>
│   │   ├── <span class="folder">📁 Models/</span>
│   │   │   ├── <span class="file">📝 User.cs</span>
│   │   │   ├── <span class="file">🎲 Game.cs</span>
│   │   │   ├── <span class="file">📈 Statistics.cs</span>
│   │   │   └── <span class="file">🔤 Word.cs</span>
│   │   ├── <span class="folder">📁 Services/</span>
│   │   │   ├── <span class="file">🎯 GameService.cs</span>
│   │   │   ├── <span class="file">📚 WordService.cs</span>
│   │   │   └── <span class="file">🔐 AuthService.cs</span>
│   │   ├── <span class="folder">📁 Data/</span>
│   │   │   ├── <span class="file">🗄️ ApplicationDbContext.cs</span>
│   │   │   └── <span class="file">🌱 DbInitializer.cs</span>
│   │   ├── <span class="folder">📁 Hubs/</span>
│   │   │   └── <span class="file">⚡ GameHub.cs</span>
│   │   ├── <span class="config">⚙️ Program.cs</span>
│   │   ├── <span class="config">📄 appsettings.json</span>
│   │   └── <span class="config">📦 KelimeZinciri.API.csproj</span>
│   └── <span class="folder">📁 Data/</span>
│       └── <span class="file">📚 turkce_kelimeler.json</span>
├── <span class="folder">📁 Frontend/</span>
│   ├── <span class="folder">📁 public/</span>
│   │   ├── <span class="config">🌐 index.html</span>
│   │   ├── <span class="config">📱 manifest.json</span>
│   │   └── <span class="folder">📁 assets/</span>
│   ├── <span class="folder">📁 src/</span>
│   │   ├── <span class="folder">📁 components/</span>
│   │   │   ├── <span class="file">🎮 Game/</span>
│   │   │   ├── <span class="file">📊 Statistics/</span>
│   │   │   ├── <span class="file">🏆 Leaderboard/</span>
│   │   │   └── <span class="file">👤 Profile/</span>
│   │   ├── <span class="folder">📁 pages/</span>
│   │   │   ├── <span class="file">🏠 Home.jsx</span>
│   │   │   ├── <span class="file">🎯 GamePage.jsx</span>
│   │   │   └── <span class="file">👤 ProfilePage.jsx</span>
│   │   ├── <span class="folder">📁 services/</span>
│   │   │   ├── <span class="file">🌐 api.js</span>
│   │   │   └── <span class="file">⚡ signalr.js</span>
│   │   ├── <span class="folder">📁 styles/</span>
│   │   │   └── <span class="file">🎨 global.css</span>
│   │   ├── <span class="file">⚛️ App.jsx</span>
│   │   └── <span class="file">🚀 index.js</span>
│   ├── <span class="config">📦 package.json</span>
│   └── <span class="config">⚙️ vite.config.js</span>
├── <span class="folder">📁 Database/</span>
│   ├── <span class="file">🗄️ init.sql</span>
│   └── <span class="file">📚 kelimeler.sql</span>
├── <span class="folder">📁 Docker/</span>
│   ├── <span class="config">🐳 Dockerfile.backend</span>
│   ├── <span class="config">🐳 Dockerfile.frontend</span>
│   └── <span class="config">📋 docker-compose.yml</span>
├── <span class="config">📖 README.md</span>
├── <span class="config">⚙️ .gitignore</span>
└── <span class="config">🚀 setup.bat</span>
            </div>
        </div>

        <div class="section">
            <h2>🛠️ Teknoloji Stack</h2>
            <div class="tech-stack">
                <div class="tech-item">
                    <h3>🚀 Backend</h3>
                    <ul>
                        <li>.NET 8.0 Core API</li>
                        <li>Entity Framework Core</li>
                        <li>SignalR (Real-time)</li>
                        <li>SQL Server</li>
                        <li>JWT Authentication</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h3>⚛️ Frontend</h3>
                    <ul>
                        <li>React 18 + Vite</li>
                        <li>Tailwind CSS</li>
                        <li>Framer Motion</li>
                        <li>Axios</li>
                        <li>React Query</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h3>🗄️ Database</h3>
                    <ul>
                        <li>SQL Server</li>
                        <li>Redis Cache</li>
                        <li>40,000+ Türkçe Kelime</li>
                        <li>Optimized Indexes</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h3>☁️ DevOps</h3>
                    <ul>
                        <li>Docker Containerization</li>
                        <li>Docker Compose</li>
                        <li>Nginx Proxy</li>
                        <li>Auto Setup Scripts</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎮 Özellikler</h2>
            <div class="features">
                <div class="feature">
                    <h4>🎯 Oyun Özellikleri</h4>
                    <ul>
                        <li>3 Zorluk Seviyesi</li>
                        <li>Real-time Multiplayer</li>
                        <li>Türkçe Kelime Doğrulama</li>
                        <li>Dinamik Puanlama</li>
                        <li>Zamanlayıcı Sistemi</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>👤 Kullanıcı Sistemi</h4>
                    <ul>
                        <li>JWT Authentication</li>
                        <li>Profil Yönetimi</li>
                        <li>İstatistik Takibi</li>
                        <li>Başarım Rozeti</li>
                        <li>Arkadaş Sistemi</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>🏆 Sosyal Özellikler</h4>
                    <ul>
                        <li>Global Liderboard</li>
                        <li>Günlük/Haftalık Sıralam</li>
                        <li>Turnuva Sistemi</li>
                        <li>Paylaşım Özellikleri</li>
                        <li>Clan Sistemi</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>📱 Modern UI/UX</h4>
                    <ul>
                        <li>Responsive Tasarım</li>
                        <li>5 Farklı Tema</li>
                        <li>Smooth Animasyonlar</li>
                        <li>PWA Support</li>
                        <li>Offline Mode</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔌 API Endpoints</h2>
            <div class="api-endpoint method-post">
                <strong>POST</strong> /api/auth/login - Kullanıcı girişi
            </div>
            <div class="api-endpoint method-post">
                <strong>POST</strong> /api/auth/register - Kullanıcı kaydı
            </div>
            <div class="api-endpoint method-get">
                <strong>GET</strong> /api/game/validate-word/{word} - Kelime doğrulama
            </div>
            <div class="api-endpoint method-post">
                <strong>POST</strong> /api/game/create-room - Oyun odası oluştur
            </div>
            <div class="api-endpoint method-get">
                <strong>GET</strong> /api/leaderboard/global - Global sıralama
            </div>
            <div class="api-endpoint method-get">
                <strong>GET</strong> /api/statistics/user/{userId} - Kullanıcı istatistikleri
            </div>
            <div class="api-endpoint method-post">
                <strong>POST</strong> /api/game/submit-score - Skor gönder
            </div>
        </div>

        <div class="section">
            <h2>🚀 Kurulum Adımları</h2>
            <ol>
                <li><strong>Projeyi İndir:</strong> ZIP dosyasını çıkart</li>
                <li><strong>Backend Setup:</strong> <code>cd Backend && dotnet restore</code></li>
                <li><strong>Database:</strong> SQL Server connection string'i güncelle</li>
                <li><strong>Migration:</strong> <code>dotnet ef database update</code></li>
                <li><strong>Frontend Setup:</strong> <code>cd Frontend && npm install</code></li>
                <li><strong>Çalıştır:</strong> <code>setup.bat</code> çalıştır veya Docker Compose kullan</li>
            </ol>
        </div>

        <div class="download-section">
            <h2>📥 Projeyi İndir</h2>
            <p>Tüm kaynak kodları, veritabanı scripti, kurulum dosyaları ve dokümantasyon dahil!</p>
            <button class="download-btn" onclick="downloadProject()">
                💾 Tam Projeyi İndir (ZIP)
            </button>
            <button class="download-btn" onclick="downloadBackend()">
                🚀 Sadece Backend (.NET)
            </button>
            <button class="download-btn" onclick="downloadFrontend()">
                ⚛️ Sadece Frontend (React)
            </button>
        </div>

        <div class="section">
            <h2>📚 Dokümantasyon</h2>
            <p>Proje içerisinde detaylı README.md dosyası, API dokümantasyonu ve kurulum rehberi bulunmaktadır.</p>
            <ul>
                <li><strong>Backend:</strong> .NET Core 8.0 API geliştirme rehberi</li>
                <li><strong>Frontend:</strong> React + Vite setup ve component yapısı</li>
                <li><strong>Database:</strong> SQL Server kurulum ve migration rehberi</li>
                <li><strong>Deployment:</strong> Docker ve cloud deployment rehberi</li>
            </ul>
        </div>

        <div class="section">
            <h2>🎯 Gelecek Özellikler (Roadmap)</h2>
            <div class="features">
                <div class="feature">
                    <h4>📱 Mobil Uygulama</h4>
                    <p>React Native ile iOS ve Android uygulaması</p>
                </div>
                <div class="feature">
                    <h4>🤖 AI Bot Rakip</h4>
                    <p>Farklı seviyelerde AI rakip sistemi</p>
                </div>
                <div class="feature">
                    <h4>🌍 Çok Dilli Destek</h4>
                    <p>İngilizce, Almanca, Fransızca kelime desteği</p>
                </div>
                <div class="feature">
                    <h4>💰 Monetizasyon</h4>
                    <p>Premium üyelik ve in-app purchase sistemi</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function downloadProject() {
            // Bu fonksiyon gerçek projede ZIP dosyası indirme işlemini yapacak
            alert('Tam proje ZIP dosyası indiriliyor...\n\nİçerik:\n- .NET Core Backend\n- React Frontend\n- SQL Server Database\n- Docker Files\n- Setup Scripts\n- Dokümantasyon');
            
            // Simulated download - gerçek projede actual file download olacak
            const link = document.createElement('a');
            link.href = '#';
            link.download = 'KelimeZinciri-FullProject.zip';
            link.click();
        }

        function downloadBackend() {
            alert('.NET Core Backend projesi indiriliyor...');
        }

        function downloadFrontend() {
            alert('React Frontend projesi indiriliyor...');
        }

        // Animated counters
        function animateCounters() {
            const counters = document.querySelectorAll('.counter');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;
                
                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.ceil(current).toLocaleString();
                        setTimeout(updateCounter, 20);
                    } else {
                        counter.textContent = target.toLocaleString();
                    }
                };
                
                updateCounter();
            });
        }

        // Initialize animations
        document.addEventListener('DOMContentLoaded', () => {
            animateCounters();
        });
    </script>
</body>
</html>